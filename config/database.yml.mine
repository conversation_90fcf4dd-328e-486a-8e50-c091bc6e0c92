# Mysql configurations
default: &default
  host: 127.0.0.1
  port: 3306
  adapter: mysql2
  encoding: utf8
  collation: utf8_unicode_ci
  username: root
  password: root
  pool: <%= ENV.fetch("RAILS_MAX_THREADS") { 20 } %>
  checkout_timeout: 5000
  variables:
    sql_mode: traditional

development:
  primary:
    <<: *default
    database: openmrs
  queue:
    <<: *default
    database: queue_dev
    migrations_paths:
      - db/queue_migrate
    processing_delay_time: 10

test:
  primary:
    <<: *default
    database: khulambe_test
  queue:
    <<: *default
    database: queue_test
    migrations_paths: db/queue_migrate
    processing_delay_time: 10

production:
  primary:
    <<: *default
    database: openmrs_prod
  queue:
    <<: *default
    database: queue_prod
    migrations_paths: db/queue_migrate
    processing_delay_time: 10

# Uncomment out the following and update them accordingly if
# you need to sync hts and anc to RDS.

# metadata:
#   host: "**********"
#   username: "metadata_user"
#   password: "letmein"
#   database: "openmrs"

#hts:
#  adapter: mysql2
#  username: root
#  database:
#  password:
#  host: localhost
#  pool: 500

#anc:
#  adapter: mysql2
#  username: root
#  database:
#  password:
#  host: localhost
#  pool: 500

healthdata:
  adapter: mysql2
  username: root
  database: healthdata
  password:
  host: localhost
  pool: 500

concepts_merge_db:
  <<: *default
  database: openmrs_limbe
# metadata_server:
#  adapter: mysql2
#  username: root
#  database: openmrs
#  password:
#  host: localhost
#  pool: 500
