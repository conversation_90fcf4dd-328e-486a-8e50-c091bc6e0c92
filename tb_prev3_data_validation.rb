# TB_PREV3 Data Validation Script
# Run this in Rails console to identify clients missing required data elements

puts "=== TB_PREV3 Data Validation Script ==="
puts "Checking for clients missing required data elements..."

# Set your report dates
start_date = Date.parse('2024-01-01')  # Change this to your report start date
end_date = Date.parse('2024-03-31')    # Change this to your report end date

# Initialize the report service
report_service = ArtService::Reports::Pepfar::TbPrev3.new(start_date: start_date, end_date: end_date)

puts "\n1. FETCHING ALL POTENTIAL TPT CLIENTS..."

# Get all clients who might be on TPT
all_tpt_clients = report_service.send(:fetch_patients_on_tpt).to_a
puts "Found #{all_tpt_clients.count} potential TPT clients"

# Track clients with missing data
clients_missing_data = []
clients_with_issues = []

puts "\n2. ANALYZING EACH CLIENT FOR MISSING DATA ELEMENTS..."

all_tpt_clients.each_with_index do |client, index|
  patient_id = client['patient_id']
  arv_number = client['arv_number']
  
  puts "Checking client #{index + 1}/#{all_tpt_clients.count}: Patient ID #{patient_id}, ARV: #{arv_number}"
  
  missing_elements = []
  
  # Check basic demographic data
  missing_elements << "gender" if client['gender'].blank?
  missing_elements << "birthdate" if client['birthdate'].blank?
  missing_elements << "age_group" if client['age_group'].blank?
  missing_elements << "arv_number" if client['arv_number'].blank?
  
  # Check TPT initiation date
  if client['tpt_initiation_date'].blank?
    missing_elements << "tpt_initiation_date"
  else
    # Check if TPT initiation date is valid
    tpt_date = client['tpt_initiation_date']&.to_date
    if tpt_date && tpt_date > (start_date - 6.months)
      missing_elements << "tpt_initiation_date_too_recent (#{tpt_date})"
    end
  end
  
  # Check ART start date
  missing_elements << "art_start_date" if client['art_start_date'].blank?
  
  # Check patient outcome
  missing_elements << "outcome" if client['outcome'].blank?
  
  # Try to get individual TPT report data
  begin
    individual_report = report_service.send(:individual_tpt_report, patient_id)
    
    if individual_report.blank?
      missing_elements << "individual_tpt_report_data (entire report is blank)"
    else
      # Check individual report elements
      missing_elements << "total_pills_taken" if individual_report['total_pills_taken'].blank?
      missing_elements << "months_on_tpt" if individual_report['months_on_tpt'].blank?
      missing_elements << "total_days_on_medication" if individual_report['total_days_on_medication'].blank?
      missing_elements << "drug_concepts" if individual_report['drug_concepts'].blank?
      
      # Check if transfer_in status is properly set
      if individual_report['transfer_in'].nil?
        missing_elements << "transfer_in_status"
      end
    end
  rescue => e
    missing_elements << "individual_tpt_report_error: #{e.message}"
    clients_with_issues << {
      patient_id: patient_id,
      arv_number: arv_number,
      error: e.message,
      missing_elements: missing_elements
    }
    next
  end
  
  # Check TPT course dates
  begin
    tpt_dates = report_service.send(:client_tpt_dates, patient_id)
    if tpt_dates.blank?
      missing_elements << "tpt_course_dates (no TPT dispensing records)"
    else
      # Check if dates are valid
      tpt_dates.each_with_index do |date_record, i|
        missing_elements << "tpt_course_start_date_#{i}" if date_record['start_date'].blank?
        missing_elements << "tpt_course_end_date_#{i}" if date_record['end_date'].blank?
        missing_elements << "tpt_course_type_#{i}" if date_record['course'].blank?
      end
    end
  rescue => e
    missing_elements << "tpt_course_dates_error: #{e.message}"
  end
  
  # If client has missing elements, add to our tracking
  if missing_elements.any?
    clients_missing_data << {
      patient_id: patient_id,
      arv_number: arv_number,
      gender: client['gender'],
      age_group: client['age_group'],
      missing_elements: missing_elements
    }
  end
end

puts "\n3. SUMMARY RESULTS"
puts "=" * 50

puts "\nCLIENTS WITH MISSING DATA ELEMENTS:"
puts "Total clients missing data: #{clients_missing_data.count}"

if clients_missing_data.any?
  # Group by missing elements to see patterns
  missing_patterns = {}
  clients_missing_data.each do |client|
    pattern = client[:missing_elements].sort.join(", ")
    missing_patterns[pattern] ||= []
    missing_patterns[pattern] << client
  end
  
  puts "\nMISSING DATA PATTERNS:"
  missing_patterns.each do |pattern, clients|
    puts "\n#{clients.count} clients missing: #{pattern}"
    clients.first(5).each do |client|
      puts "  - Patient ID: #{client[:patient_id]}, ARV: #{client[:arv_number]}, Age: #{client[:age_group]}, Gender: #{client[:gender]}"
    end
    puts "  ... and #{clients.count - 5} more" if clients.count > 5
  end
end

puts "\nCLIENTS WITH PROCESSING ERRORS:"
puts "Total clients with errors: #{clients_with_issues.count}"

if clients_with_issues.any?
  clients_with_issues.first(10).each do |client|
    puts "Patient ID: #{client[:patient_id]}, ARV: #{client[:arv_number]}"
    puts "  Error: #{client[:error]}"
    puts "  Missing: #{client[:missing_elements].join(', ')}"
    puts ""
  end
end

puts "\n4. DETAILED BREAKDOWN BY DATA ELEMENT"
puts "=" * 50

# Count missing elements
element_counts = {}
clients_missing_data.each do |client|
  client[:missing_elements].each do |element|
    element_counts[element] ||= 0
    element_counts[element] += 1
  end
end

element_counts.sort_by { |_, count| -count }.each do |element, count|
  puts "#{element}: #{count} clients"
end

puts "\n5. ACTIONABLE RECOMMENDATIONS"
puts "=" * 50

if element_counts['tpt_initiation_date'] && element_counts['tpt_initiation_date'] > 0
  puts "• #{element_counts['tpt_initiation_date']} clients missing TPT initiation dates - check TPT drug orders and observations"
end

if element_counts['individual_tpt_report_data (entire report is blank)'] && element_counts['individual_tpt_report_data (entire report is blank)'] > 0
  puts "• #{element_counts['individual_tpt_report_data (entire report is blank)']} clients have no individual TPT report data - check TPT drug dispensing records"
end

if element_counts['drug_concepts'] && element_counts['drug_concepts'] > 0
  puts "• #{element_counts['drug_concepts']} clients missing drug concepts - check if TPT drugs are properly configured"
end

if element_counts['arv_number'] && element_counts['arv_number'] > 0
  puts "• #{element_counts['arv_number']} clients missing ARV numbers - check patient identifiers"
end

puts "\nScript completed. Use the above information to identify and fix data quality issues."
