# Quick TB_PREV3 Data Check - Run in Rails Console
# Copy and paste this into your Rails console

# Set your report dates
start_date = Date.parse('2024-01-01')  # Change this
end_date = Date.parse('2024-03-31')    # Change this

puts "Checking TB_PREV3 data for period: #{start_date} to #{end_date}"

# Initialize report
report = ArtService::Reports::Pepfar::TbPrev3.new(start_date: start_date, end_date: end_date)

# Get all potential TPT clients
clients = report.send(:fetch_patients_on_tpt).to_a
puts "Found #{clients.count} potential TPT clients"

# Quick check for common missing data
missing_data_summary = {
  no_arv_number: 0,
  no_gender: 0,
  no_birthdate: 0,
  no_tpt_initiation: 0,
  no_art_start_date: 0,
  no_outcome: 0,
  individual_report_blank: 0,
  individual_report_error: 0
}

problem_clients = []

clients.each_with_index do |client, i|
  puts "Checking #{i+1}/#{clients.count}: Patient #{client['patient_id']}" if i % 50 == 0
  
  issues = []
  
  # Basic checks
  if client['arv_number'].blank?
    missing_data_summary[:no_arv_number] += 1
    issues << 'no_arv_number'
  end
  
  if client['gender'].blank?
    missing_data_summary[:no_gender] += 1
    issues << 'no_gender'
  end
  
  if client['birthdate'].blank?
    missing_data_summary[:no_birthdate] += 1
    issues << 'no_birthdate'
  end
  
  if client['tpt_initiation_date'].blank?
    missing_data_summary[:no_tpt_initiation] += 1
    issues << 'no_tpt_initiation'
  end
  
  if client['art_start_date'].blank?
    missing_data_summary[:no_art_start_date] += 1
    issues << 'no_art_start_date'
  end
  
  if client['outcome'].blank?
    missing_data_summary[:no_outcome] += 1
    issues << 'no_outcome'
  end
  
  # Check individual report
  begin
    individual = report.send(:individual_tpt_report, client['patient_id'])
    if individual.blank?
      missing_data_summary[:individual_report_blank] += 1
      issues << 'individual_report_blank'
    end
  rescue => e
    missing_data_summary[:individual_report_error] += 1
    issues << "individual_report_error: #{e.message}"
  end
  
  if issues.any?
    problem_clients << {
      patient_id: client['patient_id'],
      arv_number: client['arv_number'],
      issues: issues
    }
  end
end

puts "\n=== SUMMARY ==="
puts "Total clients analyzed: #{clients.count}"
puts "Clients with issues: #{problem_clients.count}"
puts ""

missing_data_summary.each do |issue, count|
  puts "#{issue}: #{count} clients" if count > 0
end

puts "\n=== SAMPLE PROBLEM CLIENTS ==="
problem_clients.first(10).each do |client|
  puts "Patient ID: #{client[:patient_id]}, ARV: #{client[:arv_number]}"
  puts "  Issues: #{client[:issues].join(', ')}"
end

puts "\n=== SPECIFIC QUERIES TO INVESTIGATE ==="

puts "\n1. Clients missing ARV numbers:"
puts "Patient.joins(:person).where(patient_id: #{problem_clients.select { |c| c[:issues].include?('no_arv_number') }.map { |c| c[:patient_id] }.first(10)})"

puts "\n2. Clients missing TPT initiation dates:"
no_tpt_clients = problem_clients.select { |c| c[:issues].include?('no_tpt_initiation') }.map { |c| c[:patient_id] }.first(10)
puts "# Check if these patients have any TPT drug orders:"
puts "Order.joins(:drug_order).joins('INNER JOIN concept_name cn ON cn.concept_id = orders.concept_id').where('cn.name IN (\"Rifapentine\", \"Isoniazid\", \"Isoniazid/Rifapentine\")').where(patient_id: #{no_tpt_clients})"

puts "\n3. Clients with blank individual reports:"
blank_report_clients = problem_clients.select { |c| c[:issues].any? { |i| i.include?('individual_report') } }.map { |c| c[:patient_id] }.first(10)
puts "# Check TPT drug dispensing for these patients:"
puts "Order.where(patient_id: #{blank_report_clients}).joins('INNER JOIN concept_name cn ON cn.concept_id = orders.concept_id').where('cn.name IN (\"Rifapentine\", \"Isoniazid\", \"Isoniazid/Rifapentine\")')"

puts "\nScript completed!"
