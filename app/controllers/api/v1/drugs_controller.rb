# frozen_string_literal: true

require "securerandom"

module Api
  module V1
    class DrugsController < ApplicationController
      before_action :authenticate, except: %i[print_barcode]

      def show
        render json: Drug.find(params[:id])
      end

      def index
        filters = params.permit(%i[name concept_set])

        render json: paginate(service.find_drugs(filters))
      end

      def OPD_drugslist
        filters = params.permit(%i[name])
        render json: paginate(service.find_drug_list(filters))
      end

      def arv_drugs
        render json: Drug.arv_drugs, status: :ok
      end

      def tb_drugs
        render json: Drug.tb_drugs, status: :ok
      end

      def bp_drugs
        render json: Drug.bp_drugs, status: :ok
      end

      def drug_sets
        drug_sets = {}
        set_names = {}
        set_descriptions = {}
        GeneralSet.where(["status =?", "active"]).each do |set|
          drug_sets[set.set_id] = {}
          set_names[set.set_id] = set.name
          set_descriptions[set.set_id] = set.description

          dsets = DrugSet.where(["set_id =? AND voided =?", set.set_id, 0])
          dsets.each do |d_set|
            drug_sets[set.set_id][d_set.drug_inventory_id] = {}
            drug = Drug.find(d_set.drug_inventory_id)
            drug_sets[set.set_id][d_set.drug_inventory_id]["drug_name"] = drug.name
            drug_sets[set.set_id][d_set.drug_inventory_id]["units"] = drug.units
            drug_sets[set.set_id][d_set.drug_inventory_id]["duration"] = d_set.duration
            drug_sets[set.set_id][d_set.drug_inventory_id]["frequency"] = d_set.frequency
            drug_sets[set.set_id][d_set.drug_inventory_id]["dose"] = drug.dose_strength
          end
        end
        render json: { drug_sets:,
                       set_names:,
                       set_descriptions: }
      end

      def create_drug_sets
        set_name = params[:name]
        set_desc = params[:description]
        set_drugs = params[:drugs]
        date = begin
            params[:datetime].to_date
          rescue StandardError
            Date.today
          end
        results = {}

        # set_id = params[:set_id]

        unless set_name.blank?
          ActiveRecord::Base.transaction do
            set = GeneralSet.create(name: set_name,
                                    description: set_desc,
                                    status: "active",
                                    date_created: date,
                                    date_updated: date,
                                    creator: User.current.id)
            # set.save!
            set_id = set.set_id

            unless set_id.blank?
              results["set"] = {
                "name": set.name,
                "description": set.description,
                "date_created": set.date_created,
                "date_updated": set.date_updated,
              }
              (set_drugs || []).each do |drug|
                d = Drug.find_by name: drug["drug"]

                drug_set = DrugSet.create(
                  drug_inventory_id: d.id,
                  set_id: set_id.to_i,
                  frequency: drug["frequency"],
                  duration: drug["quantity"].to_i,
                  date_created: date,
                  date_updated: date,
                  creator: User.current.id,
                )

                results["set_drugs"] = [] if results["set_drugs"].blank?

                results["set_drugs"] << { drug_id: d.id, drug_name: d.name,
                                          frequency: drug_set.frequency, quantity: drug_set.duration }
              end
            end
          end
        end

        render json: results
      end

      def void_drug_sets
        drug_set = GeneralSet.find(params[:id])
        drug_set.deactivate(params[:date].to_date) # User.current, "Voided by #{User.current.username}"
      end

      def print_barcode
        quantity = params.require(:quantity)
        data = service.print_drug_barcode(drug, quantity)

        render_zpl(data)
      end

      def tb_side_effects_drug
        render json: Drug.tb_side_effects_drug
      end

      def stock_levels
        levels = service.stock_levels(params[:classification])
        render json: levels
      end

      private

      def drug
        Drug.find(params[:drug_id])
      end

      def service
        DrugService.new
      end
    end
  end
end
