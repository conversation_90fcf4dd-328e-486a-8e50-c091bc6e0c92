# Test TB_PREV3 validation in Rails console
# Copy and paste this into Rails console to test the new validation features

puts "Testing TB_PREV3 validation features..."

# Set your report dates
start_date = Date.parse('2024-01-01')  # Change this to your actual dates
end_date = Date.parse('2024-03-31')    # Change this to your actual dates

begin
  # Initialize the report
  report_service = ArtService::Reports::Pepfar::TbPrev3.new(start_date: start_date, end_date: end_date)
  
  puts "Generating TB_PREV3 report with validation..."
  report = report_service.find_report
  
  # Check if validation data is present
  if report[:data_validation]
    puts "\n✅ Validation data found in report!"
    
    validation = report[:data_validation]
    puts "Total clients: #{validation[:total_clients_found]}"
    puts "Clients with missing data: #{validation[:clients_with_missing_data].count}"
    puts "Clients with errors: #{validation[:clients_with_errors].count}"
    
    # Display the formatted summary
    if report[:validation_summary]
      puts "\n" + "="*60
      puts report[:validation_summary]
      puts "="*60
    end
    
    # Show some sample missing data
    if validation[:clients_with_missing_data].any?
      puts "\nSample clients with missing data:"
      validation[:clients_with_missing_data].first(5).each do |client|
        puts "  Patient #{client[:patient_id]} (ARV: #{client[:arv_number]}): #{client[:missing_elements].join(', ')}"
      end
    end
    
  else
    puts "❌ No validation data found in report"
  end
  
rescue => e
  puts "❌ Error running report: #{e.message}"
  puts e.backtrace.first(5)
end

puts "\nTest completed!"
