# frozen_string_literal: true

require 'rails_helper'

RSpec.describe ArtService::Reports::Pepfar::TxHivHtn, type: :unit do
  let(:start_date) { Date.parse('2024-01-01') }
  let(:end_date) { Date.parse('2024-03-31') }
  let(:report) { described_class.new(start_date: start_date, end_date: end_date) }

  describe 'controlled HTN logic validation' do
    before do
      # Initialize the report structure
      allow(report).to receive(:pepfar_age_groups).and_return(['25-29', '30-34'])
      report.send(:init_report)
    end

    describe '#map_results' do
      context 'controlled HTN scenarios' do
        it 'includes patient with all required criteria for controlled HTN' do
          patients = [{
            'patient_id' => 1,
            'age_group' => '25-29',
            'gender' => 'M',
            'systolic' => 130.0,           # Below threshold (< 140)
            'diastolic' => 80.0,           # Below threshold (< 90)
            'diagonised' => 1,             # Diagnosed with HTN
            'date_diagnosed' => start_date - 10.days,
            'had_previous_high_bp' => 1,   # Had previous high BP
            'maternal_status' => 'Male'
          }]

          report.send(:map_results, patients: patients)
          result = report.instance_variable_get(:@report)

          expect(result['25-29']['M'][:controlled_htn]).to include(1)
          expect(result['All']['Male'][:controlled_htn]).to include(1)
        end

        it 'excludes patient without HTN diagnosis' do
          patients = [{
            'patient_id' => 2,
            'age_group' => '25-29',
            'gender' => 'M',
            'systolic' => 130.0,
            'diastolic' => 80.0,
            'diagonised' => 0,             # NOT diagnosed with HTN
            'date_diagnosed' => nil,
            'had_previous_high_bp' => 1,
            'maternal_status' => 'Male'
          }]

          report.send(:map_results, patients: patients)
          result = report.instance_variable_get(:@report)

          expect(result['25-29']['M'][:controlled_htn]).not_to include(2)
        end

        it 'excludes patient without previous high BP' do
          patients = [{
            'patient_id' => 3,
            'age_group' => '25-29',
            'gender' => 'M',
            'systolic' => 130.0,
            'diastolic' => 80.0,
            'diagonised' => 1,
            'date_diagnosed' => start_date - 10.days,
            'had_previous_high_bp' => 0,   # NO previous high BP
            'maternal_status' => 'Male'
          }]

          report.send(:map_results, patients: patients)
          result = report.instance_variable_get(:@report)

          expect(result['25-29']['M'][:controlled_htn]).not_to include(3)
        end

        it 'excludes patient with current high systolic BP' do
          patients = [{
            'patient_id' => 4,
            'age_group' => '25-29',
            'gender' => 'M',
            'systolic' => 150.0,           # High systolic (>= 140)
            'diastolic' => 80.0,
            'diagonised' => 1,
            'date_diagnosed' => start_date - 10.days,
            'had_previous_high_bp' => 1,
            'maternal_status' => 'Male'
          }]

          report.send(:map_results, patients: patients)
          result = report.instance_variable_get(:@report)

          expect(result['25-29']['M'][:controlled_htn]).not_to include(4)
        end

        it 'excludes patient with current high diastolic BP' do
          patients = [{
            'patient_id' => 5,
            'age_group' => '25-29',
            'gender' => 'M',
            'systolic' => 130.0,
            'diastolic' => 95.0,           # High diastolic (>= 90)
            'diagonised' => 1,
            'date_diagnosed' => start_date - 10.days,
            'had_previous_high_bp' => 1,
            'maternal_status' => 'Male'
          }]

          report.send(:map_results, patients: patients)
          result = report.instance_variable_get(:@report)

          expect(result['25-29']['M'][:controlled_htn]).not_to include(5)
        end

        it 'excludes patient with missing BP readings' do
          patients = [{
            'patient_id' => 6,
            'age_group' => '25-29',
            'gender' => 'M',
            'systolic' => nil,             # Missing systolic
            'diastolic' => nil,            # Missing diastolic
            'diagonised' => 1,
            'date_diagnosed' => start_date - 10.days,
            'had_previous_high_bp' => 1,
            'maternal_status' => 'Male'
          }]

          report.send(:map_results, patients: patients)
          result = report.instance_variable_get(:@report)

          expect(result['25-29']['M'][:controlled_htn]).not_to include(6)
        end
      end

      context 'boundary testing' do
        it 'excludes patient with systolic exactly at threshold' do
          patients = [{
            'patient_id' => 7,
            'age_group' => '25-29',
            'gender' => 'M',
            'systolic' => 140.0,           # Exactly at threshold (not < 140)
            'diastolic' => 80.0,
            'diagonised' => 1,
            'date_diagnosed' => start_date - 10.days,
            'had_previous_high_bp' => 1,
            'maternal_status' => 'Male'
          }]

          report.send(:map_results, patients: patients)
          result = report.instance_variable_get(:@report)

          expect(result['25-29']['M'][:controlled_htn]).not_to include(7)
        end

        it 'excludes patient with diastolic exactly at threshold' do
          patients = [{
            'patient_id' => 8,
            'age_group' => '25-29',
            'gender' => 'M',
            'systolic' => 130.0,
            'diastolic' => 90.0,           # Exactly at threshold (not < 90)
            'diagonised' => 1,
            'date_diagnosed' => start_date - 10.days,
            'had_previous_high_bp' => 1,
            'maternal_status' => 'Male'
          }]

          report.send(:map_results, patients: patients)
          result = report.instance_variable_get(:@report)

          expect(result['25-29']['M'][:controlled_htn]).not_to include(8)
        end

        it 'includes patient with BP just below thresholds' do
          patients = [{
            'patient_id' => 9,
            'age_group' => '25-29',
            'gender' => 'M',
            'systolic' => 139.0,           # Just below threshold
            'diastolic' => 89.0,           # Just below threshold
            'diagonised' => 1,
            'date_diagnosed' => start_date - 10.days,
            'had_previous_high_bp' => 1,
            'maternal_status' => 'Male'
          }]

          report.send(:map_results, patients: patients)
          result = report.instance_variable_get(:@report)

          expect(result['25-29']['M'][:controlled_htn]).to include(9)
        end
      end

      context 'newly diagnosed HTN logic' do
        it 'includes patient diagnosed within reporting period' do
          patients = [{
            'patient_id' => 10,
            'age_group' => '25-29',
            'gender' => 'M',
            'systolic' => 130.0,
            'diastolic' => 80.0,
            'diagonised' => 1,
            'date_diagnosed' => start_date + 10.days,  # Within reporting period
            'had_previous_high_bp' => 1,
            'maternal_status' => 'Male'
          }]

          report.send(:map_results, patients: patients)
          result = report.instance_variable_get(:@report)

          expect(result['25-29']['M'][:newly_diagnosed_htn]).to include(10)
        end

        it 'excludes patient diagnosed before reporting period' do
          patients = [{
            'patient_id' => 11,
            'age_group' => '25-29',
            'gender' => 'M',
            'systolic' => 130.0,
            'diastolic' => 80.0,
            'diagonised' => 1,
            'date_diagnosed' => start_date - 10.days,  # Before reporting period
            'had_previous_high_bp' => 1,
            'maternal_status' => 'Male'
          }]

          report.send(:map_results, patients: patients)
          result = report.instance_variable_get(:@report)

          expect(result['25-29']['M'][:newly_diagnosed_htn]).not_to include(11)
        end

        it 'handles nil date_diagnosed gracefully' do
          patients = [{
            'patient_id' => 12,
            'age_group' => '25-29',
            'gender' => 'M',
            'systolic' => 130.0,
            'diastolic' => 80.0,
            'diagonised' => 1,
            'date_diagnosed' => nil,  # Nil date
            'had_previous_high_bp' => 1,
            'maternal_status' => 'Male'
          }]

          expect { report.send(:map_results, patients: patients) }.not_to raise_error
          
          result = report.instance_variable_get(:@report)
          expect(result['25-29']['M'][:newly_diagnosed_htn]).not_to include(12)
        end
      end
    end
  end

  describe 'constants and thresholds' do
    it 'defines correct systolic threshold' do
      expect(described_class::SYSTOLIC_THRESHOLD).to eq(140)
    end

    it 'defines correct diastolic threshold' do
      expect(described_class::DIASTOLIC_THRESHOLD).to eq(90)
    end
  end

  describe 'age group exclusions' do
    it 'excludes children age groups from processing' do
      children_groups = report.children_age_groups
      
      patients = [{
        'patient_id' => 13,
        'age_group' => '5-9 years',  # Child age group
        'gender' => 'M',
        'systolic' => 130.0,
        'diastolic' => 80.0,
        'diagonised' => 1,
        'date_diagnosed' => start_date - 10.days,
        'had_previous_high_bp' => 1,
        'maternal_status' => 'Male'
      }]

      report.send(:init_report)
      report.send(:map_results, patients: patients)
      result = report.instance_variable_get(:@report)

      # Child should be skipped entirely
      expect(result.keys).not_to include('5-9 years')
    end
  end
end
