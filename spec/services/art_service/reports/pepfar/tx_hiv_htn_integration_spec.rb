# frozen_string_literal: true

require 'rails_helper'

RSpec.describe ArtService::Reports::Pepfar::TxHivHtn, type: :integration do
  let(:start_date) { Date.parse('2024-01-01') }
  let(:end_date) { Date.parse('2024-03-31') }
  
  describe 'controlled HTN logic integration test' do
    before do
      # Setup required concepts and encounter types
      setup_concepts_and_encounter_types
      
      # Create the report instance
      @report = described_class.new(start_date: start_date, end_date: end_date)
      
      # Mock the temp tables that would normally be created by other services
      allow(@report).to receive(:screened_for_htn).and_return(mock_patient_data)
    end

    it 'correctly identifies controlled HTN patients' do
      result = @report.find_report
      
      # Patient 1: Should be in controlled HTN (has previous high BP + current controlled BP + diagnosed)
      expect(patient_in_controlled_htn?(result, 1)).to be true
      
      # Patient 2: Should NOT be in controlled HTN (no previous high BP)
      expect(patient_in_controlled_htn?(result, 2)).to be false
      
      # Patient 3: Should NOT be in controlled HTN (current BP not controlled)
      expect(patient_in_controlled_htn?(result, 3)).to be false
      
      # Patient 4: Should NOT be in controlled HTN (not diagnosed)
      expect(patient_in_controlled_htn?(result, 4)).to be false
    end

    it 'correctly populates all indicators' do
      result = @report.find_report
      
      # All patients should be in tx_curr
      expect(count_in_indicator(result, :tx_curr)).to eq(4)
      
      # All patients have BP readings, so should be screened
      expect(count_in_indicator(result, :screened_for_htn)).to eq(4)
      
      # Only patients 1, 2, 3 are diagnosed
      expect(count_in_indicator(result, :ever_diagnosed_htn)).to eq(3)
      
      # Only patient 1 should have controlled HTN
      expect(count_in_indicator(result, :controlled_htn)).to eq(1)
    end

    private

    def setup_concepts_and_encounter_types
      # Create concepts if they don't exist
      unless ConceptName.find_by(name: 'Systolic blood pressure')
        concept = Concept.create!(datatype_id: 1, class_id: 1, creator: 1, date_created: Time.current, uuid: SecureRandom.uuid)
        ConceptName.create!(name: 'Systolic blood pressure', concept: concept, creator: 1, locale: 'en', date_created: Time.current, uuid: SecureRandom.uuid)
      end
      
      unless ConceptName.find_by(name: 'Diastolic blood pressure')
        concept = Concept.create!(datatype_id: 1, class_id: 1, creator: 1, date_created: Time.current, uuid: SecureRandom.uuid)
        ConceptName.create!(name: 'Diastolic blood pressure', concept: concept, creator: 1, locale: 'en', date_created: Time.current, uuid: SecureRandom.uuid)
      end
      
      unless EncounterType.find_by(name: 'VITALS')
        EncounterType.create!(name: 'VITALS', creator: 1, date_created: Time.current, uuid: SecureRandom.uuid)
      end
    end

    def mock_patient_data
      [
        {
          'patient_id' => 1,
          'age_group' => '25-29',
          'gender' => 'M',
          'systolic' => 130.0,
          'diastolic' => 80.0,
          'date_screened_for_htn' => start_date + 30.days,
          'diagonised' => 1,
          'date_diagnosed' => start_date - 10.days,
          'had_previous_high_bp' => 1,  # This patient had previous high BP
          'maternal_status' => 'Male'
        },
        {
          'patient_id' => 2,
          'age_group' => '30-34',
          'gender' => 'F',
          'systolic' => 125.0,
          'diastolic' => 75.0,
          'date_screened_for_htn' => start_date + 30.days,
          'diagonised' => 1,
          'date_diagnosed' => start_date - 10.days,
          'had_previous_high_bp' => 0,  # This patient did NOT have previous high BP
          'maternal_status' => 'FNP'
        },
        {
          'patient_id' => 3,
          'age_group' => '35-39',
          'gender' => 'M',
          'systolic' => 150.0,  # Current BP is high (not controlled)
          'diastolic' => 95.0,
          'date_screened_for_htn' => start_date + 30.days,
          'diagonised' => 1,
          'date_diagnosed' => start_date - 10.days,
          'had_previous_high_bp' => 1,  # Had previous high BP but current is not controlled
          'maternal_status' => 'Male'
        },
        {
          'patient_id' => 4,
          'age_group' => '40-44',
          'gender' => 'F',
          'systolic' => 120.0,
          'diastolic' => 70.0,
          'date_screened_for_htn' => start_date + 30.days,
          'diagonised' => 0,  # Not diagnosed with HTN
          'date_diagnosed' => nil,
          'had_previous_high_bp' => 1,  # Had previous high BP but not diagnosed
          'maternal_status' => 'FNP'
        }
      ]
    end

    def patient_in_controlled_htn?(result, patient_id)
      result.values.any? do |age_group_data|
        age_group_data.values.any? do |gender_data|
          gender_data[:controlled_htn]&.include?(patient_id)
        end
      end
    end

    def count_in_indicator(result, indicator)
      patient_ids = Set.new
      result.each do |age_group, age_group_data|
        next if age_group == 'All'
        age_group_data.each do |gender, gender_data|
          patient_ids.merge(gender_data[indicator] || [])
        end
      end
      patient_ids.size
    end
  end

  describe 'SQL query validation' do
    let(:report) { described_class.new(start_date: start_date, end_date: end_date) }

    it 'generates valid SQL without syntax errors' do
      expect { report.send(:screened_for_htn) }.not_to raise_error
    end

    it 'includes required fields in the result' do
      # Mock the database connection to return a sample result
      sample_result = [
        {
          'patient_id' => 1,
          'age_group' => '25-29',
          'gender' => 'M',
          'systolic' => 130.0,
          'diastolic' => 80.0,
          'date_screened_for_htn' => start_date + 30.days,
          'diagonised' => 1,
          'date_diagnosed' => start_date - 10.days,
          'had_previous_high_bp' => 1,
          'maternal_status' => 'Male'
        }
      ]
      
      allow(ActiveRecord::Base.connection).to receive(:select_all).and_return(sample_result)
      
      result = report.send(:screened_for_htn)
      
      expect(result.first).to include(
        'patient_id',
        'age_group',
        'gender',
        'systolic',
        'diastolic',
        'diagonised',
        'had_previous_high_bp',
        'maternal_status'
      )
    end
  end

  describe 'threshold constants' do
    it 'uses correct hypertension thresholds' do
      expect(described_class::SYSTOLIC_THRESHOLD).to eq(140)
      expect(described_class::DIASTOLIC_THRESHOLD).to eq(90)
    end
  end

  describe 'age group filtering' do
    let(:report) { described_class.new(start_date: start_date, end_date: end_date) }

    it 'excludes children age groups from processing' do
      expect(report.children_age_groups).to include('<1 year', '1-4 years', '5-9 years', '10-14 years')
    end

    it 'processes adult age groups' do
      adult_age_groups = report.send(:pepfar_age_groups) - report.children_age_groups
      expect(adult_age_groups).to include('15-19', '20-24', '25-29', '30-34', '35-39', '40-44', '45-49', '50+')
    end
  end
end
