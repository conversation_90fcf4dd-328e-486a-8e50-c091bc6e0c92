# PEPFAR Reports Testing

This directory contains comprehensive tests for PEPFAR reports in the ART service. The tests are designed to ensure data accuracy, prevent regressions, and validate complex business logic.

## Test Structure

### Test Types

1. **Comprehensive Tests** (`*_spec.rb`)
   - Full end-to-end testing with real database interactions
   - Scenario-based tests covering all possible patient states
   - Data integrity validation
   - Performance testing with multiple patients

2. **Integration Tests** (`*_integration_spec.rb`)
   - Focused testing with mocked data
   - SQL query validation
   - Field validation
   - Cross-service integration testing

3. **Unit Tests** (`*_logic_spec.rb`)
   - Pure logic testing of specific methods
   - Boundary testing for threshold values
   - Edge case handling
   - Isolated testing without database dependencies

## Current Test Coverage

### TX_HIV_HTN Report Tests

The TX_HIV_HTN report has comprehensive test coverage including:

#### Files:
- `tx_hiv_htn_spec.rb` - Main comprehensive tests
- `tx_hiv_htn_integration_spec.rb` - Integration tests
- `tx_hiv_htn_controlled_logic_spec.rb` - Unit tests for controlled HTN logic

#### Key Scenarios Tested:

**✅ Controlled HTN Logic**
- Patient with previous high BP + current controlled BP + diagnosed = INCLUDED
- Patient with no previous high BP = EXCLUDED
- Patient with current high BP = EXCLUDED
- Patient not diagnosed with HTN = EXCLUDED

**✅ Boundary Testing**
- Systolic exactly 140 = EXCLUDED (must be < 140)
- Diastolic exactly 90 = EXCLUDED (must be < 90)
- Values just below thresholds = INCLUDED

**✅ Error Handling**
- Nil `date_diagnosed` values don't cause crashes
- Missing BP readings are handled gracefully
- Children age groups are properly excluded

**✅ Data Integrity**
- All patients in controlled HTN are also in diagnosed HTN
- All screened patients are subset of tx_curr
- Maternal status aggregation works correctly

## Running Tests

### All PEPFAR Report Tests
```bash
bundle exec rspec spec/services/art_service/reports/pepfar/
```

### Specific Report Tests
```bash
# TX_HIV_HTN tests
bundle exec rspec spec/services/art_service/reports/pepfar/tx_hiv_htn*

# Individual test files
bundle exec rspec spec/services/art_service/reports/pepfar/tx_hiv_htn_spec.rb
bundle exec rspec spec/services/art_service/reports/pepfar/tx_hiv_htn_integration_spec.rb
bundle exec rspec spec/services/art_service/reports/pepfar/tx_hiv_htn_controlled_logic_spec.rb
```

### With Documentation Format
```bash
bundle exec rspec spec/services/art_service/reports/pepfar/tx_hiv_htn* --format documentation
```

### Running Specific Test Cases
```bash
# Run only controlled HTN logic tests
bundle exec rspec spec/services/art_service/reports/pepfar/tx_hiv_htn_controlled_logic_spec.rb -e "controlled HTN"

# Run only boundary tests
bundle exec rspec spec/services/art_service/reports/pepfar/tx_hiv_htn_controlled_logic_spec.rb -e "boundary"
```

## Test Database Setup

Before running tests, ensure the test database is properly set up:

```bash
bin/initial_database_setup.sh test mpc
```

## Writing New Report Tests

When adding tests for new PEPFAR reports, follow this structure:

### 1. Main Test File (`report_name_spec.rb`)
```ruby
require 'rails_helper'

RSpec.describe ArtService::Reports::Pepfar::ReportName, type: :service do
  let(:start_date) { Date.parse('2024-01-01') }
  let(:end_date) { Date.parse('2024-03-31') }
  let(:report) { described_class.new(start_date: start_date, end_date: end_date) }

  describe '#find_report' do
    context 'when patient meets criteria' do
      # Test scenarios
    end
  end
end
```

### 2. Integration Test File (`report_name_integration_spec.rb`)
```ruby
require 'rails_helper'

RSpec.describe ArtService::Reports::Pepfar::ReportName, type: :integration do
  # Integration tests with mocked data
end
```

### 3. Unit Test File (`report_name_logic_spec.rb`)
```ruby
require 'rails_helper'

RSpec.describe ArtService::Reports::Pepfar::ReportName, type: :unit do
  # Pure logic tests
end
```

## Test Patterns and Best Practices

### 1. Use Descriptive Context Blocks
```ruby
context 'when patient has controlled HTN' do
  context 'with previous high BP and current normal BP' do
    # Test implementation
  end
end
```

### 2. Test Edge Cases
- Boundary values (exactly at thresholds)
- Nil values
- Empty datasets
- Invalid data

### 3. Validate Data Integrity
```ruby
it 'maintains logical consistency between indicators' do
  # Ensure subset relationships are maintained
  expect(controlled_count).to be <= diagnosed_count
end
```

### 4. Use Factories for Test Data
```ruby
let!(:patient) { create(:patient, birthdate: 30.years.ago, gender: 'M') }
```

### 5. Mock External Dependencies
```ruby
before do
  allow(report).to receive(:screened_for_htn).and_return(mock_data)
end
```

## Debugging Test Failures

### Common Issues and Solutions

1. **Database State Issues**
   ```bash
   # Reset test database
   RAILS_ENV=test bin/rails db:reset
   ```

2. **Factory Issues**
   ```bash
   # Check factory definitions
   bundle exec rspec spec/factories/
   ```

3. **SQL Query Issues**
   - Add `puts` statements to inspect generated SQL
   - Use database logs to debug query execution

### Debugging Commands
```bash
# Run with verbose output
bundle exec rspec spec/services/art_service/reports/pepfar/tx_hiv_htn_spec.rb --format documentation

# Run with backtrace
bundle exec rspec spec/services/art_service/reports/pepfar/tx_hiv_htn_spec.rb --backtrace

# Run specific test with debugging
bundle exec rspec spec/services/art_service/reports/pepfar/tx_hiv_htn_spec.rb:45
```

## Contributing

When adding new tests:

1. Follow the existing naming conventions
2. Include comprehensive test coverage (happy path, edge cases, error conditions)
3. Add integration tests for complex business logic
4. Update this README with new test information
5. Ensure all tests pass before submitting PR

## Test Coverage Goals

- **Business Logic**: 100% coverage of all indicator calculations
- **Edge Cases**: All boundary conditions and error scenarios
- **Data Integrity**: Validation of logical relationships between indicators
- **Performance**: Tests with realistic data volumes
- **Regression Prevention**: Tests for all previously identified bugs

For questions or issues with testing, refer to the main project README or contact the development team.
