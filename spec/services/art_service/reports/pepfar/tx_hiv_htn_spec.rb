# frozen_string_literal: true

require 'rails_helper'

RSpec.describe ArtService::Reports::Pepfar::TxHivHtn, type: :service do
  let(:start_date) { Date.parse('2024-01-01') }
  let(:end_date) { Date.parse('2024-03-31') }
  let(:program) { create(:program, name: 'HIV Program') }
  let(:location) { create(:location) }
  
  # Encounter types
  let(:vitals_encounter_type) { create(:encounter_type, name: 'VITALS') }
  let(:consultation_encounter_type) { create(:encounter_type, name: 'HIV CLINIC CONSULTATION') }
  
  # Concepts
  let(:systolic_bp_concept) { create(:concept_name, name: 'Systolic blood pressure') }
  let(:diastolic_bp_concept) { create(:concept_name, name: 'Diastolic blood pressure') }
  let(:htn_diagnosis_date_concept) { create(:concept_name, name: 'Hypertension diagnosis date') }
  
  let(:report) { described_class.new(start_date: start_date, end_date: end_date) }

  before do
    # Create temp tables that the report depends on
    create_temp_earliest_start_date_table
    create_temp_patient_outcomes_table
    create_temp_maternal_status_table
  end

  after do
    # Clean up temp tables
    ActiveRecord::Base.connection.execute('DROP TABLE IF EXISTS temp_earliest_start_date')
    ActiveRecord::Base.connection.execute('DROP TABLE IF EXISTS temp_patient_outcomes')
    ActiveRecord::Base.connection.execute('DROP TABLE IF EXISTS temp_maternal_status')
  end

  describe '#find_report' do
    context 'when patient has no BP readings' do
      let!(:patient) { create_patient_on_art }

      it 'includes patient in tx_curr but not in other indicators' do
        result = report.find_report
        
        expect(patient_in_indicator?(result, patient, :tx_curr)).to be true
        expect(patient_in_indicator?(result, patient, :screened_for_htn)).to be false
        expect(patient_in_indicator?(result, patient, :ever_diagnosed_htn)).to be false
        expect(patient_in_indicator?(result, patient, :newly_diagnosed_htn)).to be false
        expect(patient_in_indicator?(result, patient, :controlled_htn)).to be false
      end
    end

    context 'when patient has BP readings in reporting period' do
      let!(:patient) { create_patient_on_art }

      before do
        create_bp_reading(patient, systolic: 120, diastolic: 80, date: start_date + 30.days)
      end

      it 'includes patient in tx_curr and screened_for_htn' do
        result = report.find_report
        
        expect(patient_in_indicator?(result, patient, :tx_curr)).to be true
        expect(patient_in_indicator?(result, patient, :screened_for_htn)).to be true
        expect(patient_in_indicator?(result, patient, :ever_diagnosed_htn)).to be false
        expect(patient_in_indicator?(result, patient, :controlled_htn)).to be false
      end
    end

    context 'when patient is diagnosed with HTN' do
      let!(:patient) { create_patient_on_art }

      before do
        create_bp_reading(patient, systolic: 120, diastolic: 80, date: start_date + 30.days)
        create_htn_diagnosis(patient, date: start_date - 10.days)
      end

      it 'includes patient in ever_diagnosed_htn' do
        result = report.find_report
        
        expect(patient_in_indicator?(result, patient, :tx_curr)).to be true
        expect(patient_in_indicator?(result, patient, :screened_for_htn)).to be true
        expect(patient_in_indicator?(result, patient, :ever_diagnosed_htn)).to be true
        expect(patient_in_indicator?(result, patient, :newly_diagnosed_htn)).to be false
        expect(patient_in_indicator?(result, patient, :controlled_htn)).to be false
      end
    end

    context 'when patient is newly diagnosed with HTN in reporting period' do
      let!(:patient) { create_patient_on_art }

      before do
        create_bp_reading(patient, systolic: 120, diastolic: 80, date: start_date + 30.days)
        create_htn_diagnosis(patient, date: start_date + 10.days)
      end

      it 'includes patient in newly_diagnosed_htn' do
        result = report.find_report
        
        expect(patient_in_indicator?(result, patient, :tx_curr)).to be true
        expect(patient_in_indicator?(result, patient, :screened_for_htn)).to be true
        expect(patient_in_indicator?(result, patient, :ever_diagnosed_htn)).to be true
        expect(patient_in_indicator?(result, patient, :newly_diagnosed_htn)).to be true
        expect(patient_in_indicator?(result, patient, :controlled_htn)).to be false
      end
    end

    context 'when patient has controlled HTN (with previous high BP)' do
      let!(:patient) { create_patient_on_art }

      before do
        # Previous high BP reading before reporting period
        create_bp_reading(patient, systolic: 160, diastolic: 100, date: start_date - 30.days)
        # Current controlled BP reading in reporting period
        create_bp_reading(patient, systolic: 120, diastolic: 80, date: start_date + 30.days)
        # HTN diagnosis
        create_htn_diagnosis(patient, date: start_date - 10.days)
      end

      it 'includes patient in controlled_htn' do
        result = report.find_report
        
        expect(patient_in_indicator?(result, patient, :tx_curr)).to be true
        expect(patient_in_indicator?(result, patient, :screened_for_htn)).to be true
        expect(patient_in_indicator?(result, patient, :ever_diagnosed_htn)).to be true
        expect(patient_in_indicator?(result, patient, :controlled_htn)).to be true
      end
    end

    context 'when patient has normal BP but no previous high BP' do
      let!(:patient) { create_patient_on_art }

      before do
        # Only normal BP readings
        create_bp_reading(patient, systolic: 120, diastolic: 80, date: start_date - 30.days)
        create_bp_reading(patient, systolic: 115, diastolic: 75, date: start_date + 30.days)
        # HTN diagnosis
        create_htn_diagnosis(patient, date: start_date - 10.days)
      end

      it 'does not include patient in controlled_htn' do
        result = report.find_report
        
        expect(patient_in_indicator?(result, patient, :tx_curr)).to be true
        expect(patient_in_indicator?(result, patient, :screened_for_htn)).to be true
        expect(patient_in_indicator?(result, patient, :ever_diagnosed_htn)).to be true
        expect(patient_in_indicator?(result, patient, :controlled_htn)).to be false
      end
    end

    context 'when patient has high BP in current period' do
      let!(:patient) { create_patient_on_art }

      before do
        # Previous high BP
        create_bp_reading(patient, systolic: 160, diastolic: 100, date: start_date - 30.days)
        # Current high BP (not controlled)
        create_bp_reading(patient, systolic: 150, diastolic: 95, date: start_date + 30.days)
        # HTN diagnosis
        create_htn_diagnosis(patient, date: start_date - 10.days)
      end

      it 'does not include patient in controlled_htn' do
        result = report.find_report
        
        expect(patient_in_indicator?(result, patient, :tx_curr)).to be true
        expect(patient_in_indicator?(result, patient, :screened_for_htn)).to be true
        expect(patient_in_indicator?(result, patient, :ever_diagnosed_htn)).to be true
        expect(patient_in_indicator?(result, patient, :controlled_htn)).to be false
      end
    end

    context 'edge cases for controlled HTN' do
      let!(:patient) { create_patient_on_art }

      context 'when systolic is high but diastolic is normal previously' do
        before do
          # Previous high systolic, normal diastolic
          create_bp_reading(patient, systolic: 150, diastolic: 80, date: start_date - 30.days)
          # Current controlled BP
          create_bp_reading(patient, systolic: 130, diastolic: 75, date: start_date + 30.days)
          create_htn_diagnosis(patient, date: start_date - 10.days)
        end

        it 'includes patient in controlled_htn' do
          result = report.find_report
          expect(patient_in_indicator?(result, patient, :controlled_htn)).to be true
        end
      end

      context 'when diastolic is high but systolic is normal previously' do
        before do
          # Previous normal systolic, high diastolic
          create_bp_reading(patient, systolic: 130, diastolic: 95, date: start_date - 30.days)
          # Current controlled BP
          create_bp_reading(patient, systolic: 125, diastolic: 80, date: start_date + 30.days)
          create_htn_diagnosis(patient, date: start_date - 10.days)
        end

        it 'includes patient in controlled_htn' do
          result = report.find_report
          expect(patient_in_indicator?(result, patient, :controlled_htn)).to be true
        end
      end
    end

    context 'maternal status aggregation' do
      let!(:female_patient) { create_patient_on_art(gender: 'F', maternal_status: 'FP') }
      let!(:male_patient) { create_patient_on_art(gender: 'M', maternal_status: 'Male') }

      before do
        create_bp_reading(female_patient, systolic: 120, diastolic: 80, date: start_date + 30.days)
        create_bp_reading(male_patient, systolic: 125, diastolic: 85, date: start_date + 30.days)
      end

      it 'correctly aggregates by maternal status' do
        result = report.find_report

        expect(result['All']['FP'][:tx_curr]).to include(female_patient.id)
        expect(result['All']['Male'][:tx_curr]).to include(male_patient.id)
        expect(result['All']['FP'][:screened_for_htn]).to include(female_patient.id)
        expect(result['All']['Male'][:screened_for_htn]).to include(male_patient.id)
      end
    end

    context 'age group filtering' do
      let!(:child_patient) { create_patient_on_art(birthdate: 5.years.ago) }
      let!(:adult_patient) { create_patient_on_art(birthdate: 30.years.ago) }

      before do
        create_bp_reading(child_patient, systolic: 120, diastolic: 80, date: start_date + 30.days)
        create_bp_reading(adult_patient, systolic: 125, diastolic: 85, date: start_date + 30.days)
      end

      it 'excludes children from the report' do
        result = report.find_report

        # Child should not appear in any age group
        expect(patient_in_any_age_group?(result, child_patient)).to be false
        # Adult should appear
        expect(patient_in_any_age_group?(result, adult_patient)).to be true
      end
    end

    context 'nil date_diagnosed handling' do
      let!(:patient) { create_patient_on_art }

      before do
        create_bp_reading(patient, systolic: 120, diastolic: 80, date: start_date + 30.days)
        # Create HTN diagnosis without date
        encounter = create(:encounter,
          patient: patient,
          encounter_type: consultation_encounter_type,
          encounter_datetime: start_date - 10.days,
          program: program
        )

        create(:observation,
          person: patient.person,
          encounter: encounter,
          concept: htn_diagnosis_date_concept.concept,
          value_datetime: nil,
          obs_datetime: start_date - 10.days
        )
      end

      it 'handles nil date_diagnosed without errors' do
        expect { report.find_report }.not_to raise_error

        result = report.find_report
        expect(patient_in_indicator?(result, patient, :ever_diagnosed_htn)).to be true
        expect(patient_in_indicator?(result, patient, :newly_diagnosed_htn)).to be false
      end
    end

    context 'threshold boundary testing' do
      let!(:patient) { create_patient_on_art }

      context 'when previous BP is exactly at threshold' do
        before do
          # Previous BP exactly at threshold (140/90)
          create_bp_reading(patient, systolic: 140, diastolic: 90, date: start_date - 30.days)
          # Current controlled BP
          create_bp_reading(patient, systolic: 135, diastolic: 85, date: start_date + 30.days)
          create_htn_diagnosis(patient, date: start_date - 10.days)
        end

        it 'includes patient in controlled_htn' do
          result = report.find_report
          expect(patient_in_indicator?(result, patient, :controlled_htn)).to be true
        end
      end

      context 'when current BP is exactly at threshold' do
        before do
          # Previous high BP
          create_bp_reading(patient, systolic: 150, diastolic: 95, date: start_date - 30.days)
          # Current BP exactly at threshold (should not be controlled)
          create_bp_reading(patient, systolic: 140, diastolic: 90, date: start_date + 30.days)
          create_htn_diagnosis(patient, date: start_date - 10.days)
        end

        it 'does not include patient in controlled_htn' do
          result = report.find_report
          expect(patient_in_indicator?(result, patient, :controlled_htn)).to be false
        end
      end
    end
  end

  describe 'performance and data integrity' do
    let!(:patients) { create_list(:patient, 10).map { |p| create_patient_on_art_from_existing(p) } }

    before do
      patients.each_with_index do |patient, index|
        # Create varied scenarios
        if index.even?
          create_bp_reading(patient, systolic: 160, diastolic: 100, date: start_date - 30.days)
          create_bp_reading(patient, systolic: 130, diastolic: 80, date: start_date + 30.days)
          create_htn_diagnosis(patient, date: start_date - 10.days)
        else
          create_bp_reading(patient, systolic: 120, diastolic: 80, date: start_date + 30.days)
        end
      end
    end

    it 'processes multiple patients without errors' do
      expect { report.find_report }.not_to raise_error
    end

    it 'maintains data consistency across indicators' do
      result = report.find_report

      # All patients should be in tx_curr
      expect(count_patients_in_indicator(result, :tx_curr)).to eq(patients.count)

      # Screened patients should be subset of tx_curr
      screened_count = count_patients_in_indicator(result, :screened_for_htn)
      expect(screened_count).to be <= patients.count

      # Controlled HTN should be subset of diagnosed
      controlled_count = count_patients_in_indicator(result, :controlled_htn)
      diagnosed_count = count_patients_in_indicator(result, :ever_diagnosed_htn)
      expect(controlled_count).to be <= diagnosed_count
    end
  end

  private

  def create_patient_on_art(gender: 'M', birthdate: 30.years.ago, maternal_status: nil)
    patient = create(:patient, birthdate: birthdate, gender: gender)

    maternal_status ||= gender == 'M' ? 'Male' : 'FNP'

    # Add to temp tables
    ActiveRecord::Base.connection.execute(
      "INSERT INTO temp_earliest_start_date (patient_id, birthdate, gender) VALUES (#{patient.id}, '#{patient.birthdate}', '#{patient.gender}')"
    )
    ActiveRecord::Base.connection.execute(
      "INSERT INTO temp_patient_outcomes (patient_id, pepfar_cum_outcome) VALUES (#{patient.id}, 'On antiretrovirals')"
    )
    ActiveRecord::Base.connection.execute(
      "INSERT INTO temp_maternal_status (patient_id, maternal_status) VALUES (#{patient.id}, '#{maternal_status}')"
    )

    patient
  end

  def create_patient_on_art_from_existing(patient)
    # Add existing patient to temp tables
    ActiveRecord::Base.connection.execute(
      "INSERT INTO temp_earliest_start_date (patient_id, birthdate, gender) VALUES (#{patient.id}, '#{patient.birthdate}', '#{patient.gender}')"
    )
    ActiveRecord::Base.connection.execute(
      "INSERT INTO temp_patient_outcomes (patient_id, pepfar_cum_outcome) VALUES (#{patient.id}, 'On antiretrovirals')"
    )
    maternal_status = patient.gender == 'M' ? 'Male' : 'FNP'
    ActiveRecord::Base.connection.execute(
      "INSERT INTO temp_maternal_status (patient_id, maternal_status) VALUES (#{patient.id}, '#{maternal_status}')"
    )

    patient
  end

  def create_bp_reading(patient, systolic:, diastolic:, date:)
    encounter = create(:encounter, 
      patient: patient, 
      encounter_type: vitals_encounter_type,
      encounter_datetime: date,
      program: program
    )
    
    create(:observation,
      person: patient.person,
      encounter: encounter,
      concept: systolic_bp_concept.concept,
      value_numeric: systolic,
      obs_datetime: date
    )
    
    create(:observation,
      person: patient.person,
      encounter: encounter,
      concept: diastolic_bp_concept.concept,
      value_numeric: diastolic,
      obs_datetime: date
    )
  end

  def create_htn_diagnosis(patient, date:)
    encounter = create(:encounter,
      patient: patient,
      encounter_type: consultation_encounter_type,
      encounter_datetime: date,
      program: program
    )
    
    create(:observation,
      person: patient.person,
      encounter: encounter,
      concept: htn_diagnosis_date_concept.concept,
      value_datetime: date,
      obs_datetime: date
    )
  end

  def patient_in_indicator?(result, patient, indicator)
    result.values.any? do |age_group_data|
      age_group_data.values.any? do |gender_data|
        gender_data[indicator]&.include?(patient.id)
      end
    end
  end

  def create_temp_earliest_start_date_table
    ActiveRecord::Base.connection.execute <<~SQL
      CREATE TEMPORARY TABLE temp_earliest_start_date (
        patient_id INT PRIMARY KEY,
        birthdate DATE,
        gender VARCHAR(10)
      )
    SQL
  end

  def create_temp_patient_outcomes_table
    ActiveRecord::Base.connection.execute <<~SQL
      CREATE TEMPORARY TABLE temp_patient_outcomes (
        patient_id INT PRIMARY KEY,
        pepfar_cum_outcome VARCHAR(50)
      )
    SQL
  end

  def create_temp_maternal_status_table
    ActiveRecord::Base.connection.execute <<~SQL
      CREATE TEMPORARY TABLE temp_maternal_status (
        patient_id INT PRIMARY KEY,
        maternal_status VARCHAR(10)
      )
    SQL
  end
end
