## Context
*Gives the reviewer some context about the work and why this change is being made, the WHY you are doing this. This field goes more into the product perspective.*

## Description
*Provide a detailed description of how exactly this task will be accomplished. This can be something technical. What specific steps will be taken to achieve the goal? This should include details on service integration, job logic, implementation, etc.*

## Changes in the codebase
*This is where becomes technical. Here is where you can be more focused on the engineering side of your solution. Include information about the functionality they are adding or modifying, as well as any refactoring or improvement of existing code.*

## Changes outside the codebase
*If you have made changes to external services, need to add additional values to the job settings, or need to add something new to the database, explain it here. This may include updates to third-party services, changes to infrastructure configuration, integration with external APIs, etc.*

## Aditional information
*Provide any additional information that might be useful to the reviewer in evaluating this pull request. This could include performance considerations,design choices, etc.*

## Ticket
*Link to the ticket that this PR is related to. This is important to keep track of the work and to have a reference to the context of the work.*

## Screenshots
*If the change is visual, include screenshots or gifs of the changes. This can be useful for the reviewer to understand the changes and to see if the changes are as expected.*