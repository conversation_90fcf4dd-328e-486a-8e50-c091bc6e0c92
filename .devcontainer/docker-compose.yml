version: '3.8'
services:
  devcontainer:
    build: 
      context: .
      dockerfile: Dockerfile
    volumes:
      - ../..:/workspaces:cached
    network_mode: service:db
    command: sleep infinity
  db:
    image: mysql:latest
    environment:
      MYSQL_ROOT_PASSWORD: root
      MYSQL_USER: test
      MYSQL_PASSWORD: test
    ports:
      - "3304:3306" 
    volumes:
      - ./my.cnf:/etc/mysql/my.cnf
      - db-data:/var/lib/mysql

volumes:
  db-data:
